# Account Bulk Update Locations API

## Endpoint
`POST /api/accounts/bulk-update-locations`

## Description
This endpoint allows you to update multiple account locations in a single request. It's designed to handle bulk location updates with comprehensive logging and validation.

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer {your-token}
```

### Request Body
```json
{
  "updates": [
    {
      "account_id": 123,
      "account_name": "Account Name",
      "current_latitude": 30.0444,
      "current_longitude": 31.2357,
      "new_latitude": 30.0445,
      "new_longitude": 31.2358,
      "confidence_score": 85,
      "visit_frequency": 12,
      "visits_count": 8,
      "average_distance_improvement": 45.5,
      "reason": "High visit frequency (12 visits), Low average distance (25m)"
    }
  ],
  "analysis_metadata": {
    "distance_threshold": 100,
    "analysis_date": "2025-01-17 10:30:00",
    "total_accounts_analyzed": 5,
    "total_visits_analyzed": 45
  }
}
```

### Field Descriptions

#### Required Fields
- `updates` (array): Array of account location updates
- `updates.*.account_id` (integer): ID of the account to update
- `updates.*.new_latitude` (numeric): New latitude coordinate
- `updates.*.new_longitude` (numeric): New longitude coordinate

#### Optional Fields
- `updates.*.account_name` (string): Name of the account
- `updates.*.current_latitude` (numeric): Current latitude coordinate
- `updates.*.current_longitude` (numeric): Current longitude coordinate
- `updates.*.confidence_score` (integer, 0-100): Confidence score of the location update
- `updates.*.visit_frequency` (integer): Number of visits to this location
- `updates.*.visits_count` (integer): Total visit count
- `updates.*.average_distance_improvement` (numeric): Average distance improvement in meters
- `updates.*.reason` (string): Reason for the location update
- `analysis_metadata` (object): Metadata about the analysis
- `analysis_metadata.distance_threshold` (numeric): Distance threshold used in analysis
- `analysis_metadata.analysis_date` (date): Date when analysis was performed
- `analysis_metadata.total_accounts_analyzed` (integer): Total accounts analyzed
- `analysis_metadata.total_visits_analyzed` (integer): Total visits analyzed

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "updated_count": 3,
  "message": "Successfully updated 3 account locations"
}
```

### Error Response (500 Internal Server Error)
```json
{
  "success": false,
  "updated_count": 0,
  "message": "Failed to update account locations: {error_message}"
}
```

### Validation Error Response (422 Unprocessable Entity)
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "updates.0.account_id": ["The account id field is required."],
    "updates.0.new_latitude": ["The new latitude field is required."]
  }
}
```

## Features

1. **Bulk Processing**: Update multiple accounts in a single transaction
2. **Comprehensive Logging**: All updates are logged with context information
3. **Transaction Safety**: All updates are wrapped in a database transaction
4. **Validation**: Comprehensive validation of all input data
5. **Error Handling**: Graceful error handling with rollback on failure
6. **Laravel Octane Compatible**: Stateless design for high performance

## Usage Examples

### Single Account Update
```bash
curl -X POST http://your-domain/api/accounts/bulk-update-locations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "updates": [
      {
        "account_id": 123,
        "new_latitude": 30.0445,
        "new_longitude": 31.2358,
        "reason": "Location correction based on recent visits"
      }
    ]
  }'
```

### Multiple Account Updates
```bash
curl -X POST http://your-domain/api/accounts/bulk-update-locations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "updates": [
      {
        "account_id": 123,
        "new_latitude": 30.0445,
        "new_longitude": 31.2358
      },
      {
        "account_id": 124,
        "new_latitude": 30.0446,
        "new_longitude": 31.2359
      }
    ],
    "analysis_metadata": {
      "distance_threshold": 100,
      "analysis_date": "2025-01-17 10:30:00",
      "total_accounts_analyzed": 2,
      "total_visits_analyzed": 25
    }
  }'
```

## Implementation Details

- **Service Class**: `App\Services\AccountLocationUpdateService`
- **Request Class**: `App\Http\Requests\AccountBulkUpdateLocationsRequest`
- **Controller Method**: `AccountController@bulkUpdateLocations`
- **Route Name**: `bulk_update_account_locations`
- **Database Fields Updated**: `accounts.ll` (latitude), `accounts.lg` (longitude)
