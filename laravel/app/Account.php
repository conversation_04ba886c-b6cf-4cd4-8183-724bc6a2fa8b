<?php

namespace App;

use App\Casts\CustomDate;
use App\Exceptions\CrmException;
use App\Models\NewAccountDoctor;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use App\Models\DoctorProfiling;
use App\Models\LinkedPharmacy;
use App\Models\ListType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Account extends Model
{
    //
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    use HasRelationships;

    protected $guard_name = 'api';

    protected $table = 'accounts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'type_id',
        'sub_type_id',
        'classification_id',
        'name',
        'address',
        'tel',
        'mobile',
        'email',
        'is_merged',
        'hidden_fav_list',
        'notes',
        'active_date',
        'inactive_date',
        'file_id',
        'll',
        'lg',
        'visit_id'
    ];

    // protected $appends = ['shift','type'];

    protected $casts = [
        'active_date' => CustomDate::class,
        'inactive_date' => CustomDate::class,
        'deleted_at' => CustomDate::class,
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function getShiftAttribute()
    {
        return $this->shift();
    }

    public function getTypeAttribute()
    {
        return $this->type()->first();
    }

    public function type()
    {
        return $this->belongsTo(AccountType::class, 'type_id');
    }

    public function shift()
    {
        return $this->type()->first()->shift;
    }

    public function sub_type()
    {
        return $this->belongsTo(AccountType::class, 'sub_type_id');
    }

    public function accountlines()
    {
        return $this->hasMany(AccountLines::class)
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
    }

    public function accountdoctors()
    {
        return $this->hasMany(AccountDoctors::class);
    }

    public function pharmacies()
    {
        return $this->hasMany(LinkedPharmacy::class);
    }

    public function newaccountdoctors()
    {
        return $this->hasMany(NewAccountDoctor::class);
    }

    public function actualVisits()
    {
        return $this->hasMany(ActualVisit::class);
    }


    public function recentVisits($days = 30)
    {
        return $this->actualVisits()
            ->where('visit_date', '>=', now()->subDays($days))
            ->orderBy('visit_date', 'desc');
    }

    public function doctorProfiling()
    {
        return $this->hasMany(DoctorProfiling::class);
    }

    /**
     * @deprecated
     */
    public function doctors()
    {
        return $this->belongsToMany(Doctor::class, 'new_account_doctors')
            ->withPivot(["from_date", "to_date", "created_at", "updated_at"]);
        // ->withTimestamps();
    }

    public function activeAccountDoctors()
    {
        return $this->belongsToMany(Doctor::class, 'new_account_doctors', 'account_id', 'doctor_id')
            ->withPivot(["id", "line_id", "account_lines_id", "from_date", "to_date"])
            ->where('doctors.active_date', '<=', (string)Carbon::now())
            ->where('active_date', '<=', (string)Carbon::now())
            ->where('new_account_doctors.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('inactive_date', '=', null)->orWhere('inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
    }

    public function inactiveAccountDoctors()
    {
        return $this->belongsToMany(Doctor::class, 'new_account_doctors')
            ->withPivot(["id", "line_id", "account_lines_id", "from_date", "to_date"])
            ->where(
                fn($q) => $q
                    ->where('new_account_doctors.from_date', '>', now())
                    ->orWhere('new_account_doctors.to_date', '<', (string)Carbon::now())
            );
    }

    public function activeAccountLines()
    {
        return $this->hasMany(AccountLines::class, 'account_id')
            // ->withPivot(["id", "line_id", "line_division_id", "from_date", "to_date"])
            // ->where('active_date', '<=', (string)Carbon::now())
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            // ->where(fn ($q) => $q->where('inactive_date', '=', null)->orWhere('inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
    }

    public function inactiveAccountLines()
    {
        return $this->hasMany(AccountLines::class, 'account_lines')
            // ->withPivot(["id", "line_id", "line_division_id", "from_date", "to_date"])
            ->where(
                fn($q) => $q
                    ->where('account_lines.from_date', '>', now())
                    ->orWhere('account_lines.to_date', '<', (string)Carbon::now())
            );
    }


    public function accountsocials()
    {
        return $this->hasMany(AccountSocial::class);
    }

    public function divisions(): BelongsToMany
    {
        return $this->belongsToMany(LineDivision::class, 'account_lines', 'account_id', 'line_division_id')
            ->withPivot(['from_date','to_date','deleted_at'])
            ->whereNull("account_lines.deleted_at")
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(
                fn($q) => $q->where('account_lines.to_date', '=', null)
                            ->orWhere('account_lines.to_date', '>=', (string)Carbon::now())
            );
    }

    public function bricks(): BelongsToMany
    {
        return $this->belongsToMany(Brick::class, 'account_lines', 'account_id', 'brick_id')
            ->withPivot(['from_date','to_date','deleted_at'])
            ->whereNull("account_lines.deleted_at")
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(
                fn($q) => $q->where('account_lines.to_date', '=', null)
                    ->orWhere('account_lines.to_date', '>=', (string)Carbon::now())
            );
    }

    public function lines(): BelongsToMany
    {
        return $this->belongsToMany(Line::class, 'account_lines', 'account_id', 'line_id')
            ->withPivot(['from_date','to_date','deleted_at'])
            ->whereNull("account_lines.deleted_at")
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(
                fn($q) => $q->where('account_lines.to_date', '=', null)
                    ->orWhere('account_lines.to_date', '>=', (string)Carbon::now())
            );
    }

    public static function getAccountInLineDivisionWithAuthUserOfSpecificShift($shift)
    {
        $setting = ListType::first()->type == 'Default List';
        $accounts = DB::table("accounts")
            ->select(
                "accounts.id"
            )
            // ->leftJoin("account_lines", "accounts.id", "account_lines.account_id")
            ->leftJoin("account_types", "accounts.type_id", "account_types.id")
            ->leftJoin("shifts", "account_types.shift_id", "shifts.id");
        if (!$setting) {
            $accounts = $accounts
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $accounts = $accounts->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id');
        }
        $accounts = $accounts->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->where("shifts.id", $shift)
            ->whereNull('accounts.deleted_at')
            ->whereNull('account_lines.deleted_at')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->whereNull('account_lines.deleted_at')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->whereIn('account_lines.line_division_id', User::getLineDivisionsOfAuthUser())->groupBy('id')->get();
        return $accounts;
    }

    public static function getAccountInLineDivisionWithAuthUser()
    {
        $setting = ListType::first()->type == 'Default List';
        $accounts = DB::table("accounts")
            ->select(
                "accounts.id"
            )
            // ->leftJoin("account_lines", "accounts.id", "account_lines.account_id")
            ->leftJoin("account_types", "accounts.type_id", "account_types.id")
            ->leftJoin("shifts", "account_types.shift_id", "shifts.id");
        if (!$setting) {
            $accounts = $accounts
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $accounts = $accounts->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id');
        }
        $accounts = $accounts->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->whereNull('accounts.deleted_at')
            ->whereNull('account_lines.deleted_at')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->whereNull('account_lines.deleted_at')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->whereIn('account_lines.line_division_id', User::getLineDivisionsOfAuthUser())->groupBy('id');
        return $accounts;
    }

    public static function getAccountIdsInLineDivisionWithAuthUser()
    {
        return self::getAccountInLineDivisionWithAuthUser()->get()->unique('id')->values()->pluck('id');
    }

    public static function getAccountIdsInLineDivisionWithAuthUserOfSpecificShift($shift)
    {
        return self::getAccountInLineDivisionWithAuthUserOfSpecificShift($shift)->unique('id')->values()->pluck('id');
    }

    public function specialities()
    {
        return $this->hasManyDeepFromRelations(
            $this->activeAccountDoctors(),
            (new Doctor)->speciality()
        );
    }

    public function classes()
    {
        return $this->hasManyDeepFromRelations($this->activeAccountDoctors(), (new Doctor)->doctorClass());
    }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        AccountLines::withTrashed()->where('account_id', $this->id)->restore();
        $doctors = NewAccountDoctor::where('account_id', $this->id)?->withTrashed()->pluck('doctor_id');
        Doctor::whereIn('id', $doctors)?->withTrashed()->restore();
        NewAccountDoctor::withTrashed()->where('account_id', $this->id)->restore();
        AccountSocial::withTrashed()->where('account_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        AccountLines::where('account_id', $this->id)?->withTrashed()->forceDelete();
        $doctors = NewAccountDoctor::where('account_id', $this->id)?->withTrashed()->pluck('doctor_id');
        Doctor::whereIn('id', $doctors)?->withTrashed()->forceDelete();
        NewAccountDoctor::where('account_id', $this->id)?->withTrashed()->forceDelete();
        AccountSocial::where('account_id', $this->id)?->withTrashed()->forceDelete();
        Account::withTrashed()->where('id', $this->id)?->forceDelete();
    }
}
