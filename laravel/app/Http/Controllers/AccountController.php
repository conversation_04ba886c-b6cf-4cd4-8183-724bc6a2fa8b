<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountSchedule;
use App\AccountSocial;
use App\AccountType;
use App\ActualVisit;
use App\Line;
use App\LineDivision;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Helpers\LogActivity;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Http\Requests\AccountRequest;
use App\Http\Requests\AccountBulkUpdateLocationsRequest;
use App\Http\Requests\AccountScheduleRequest;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Models\NewAccountDoctor;
use Illuminate\Support\Carbon;
use App\Imports\Updates\AccountsImport as UpdatesAccountsImport;
use App\LineUser;
use App\LinkedParmaciesSetting;
use App\Mapping;
use App\MappingUnifiedCode;
use App\Models\AccountClassification;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialDoctorCostType;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\LinkedPharmacy;
use App\PlanVisit;
use App\Role;
use App\Services\AccountLocationUpdateService;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AccountController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $accounts = Account::select([
            'accounts.id',
            'accounts.name',
            'accounts.code',
            'account_types.name as type_name',
            'active_date',
            'inactive_date'
        ])
            ->selectRaw('IFNULL(crm_account_types.name,"") AS sub_type_name')
            ->selectRaw('IFNULL(crm_accounts.address,"") AS address')
            ->selectRaw('IFNULL(crm_accounts.code,"") AS code')
            ->selectRaw('IFNULL(crm_accounts.tel,"") AS tel')
            ->selectRaw('IFNULL(crm_accounts.mobile,"") AS mobile')
            ->selectRaw('IFNULL(crm_accounts.email,"") AS email')
            ->selectRaw('IFNULL(crm_accounts.notes,"") AS notes')
            ->selectRaw('IFNULL(crm_account_classifications.name,"") AS classification')
            // ->selectRaw('IF(crm_accounts.active_date,date_format(crm_accounts.active_date, \'%Y-%m-%d\'),"") AS active_date')
            // ->selectRaw('IF(crm_accounts.inactive_date,date_format(crm_accounts.inactive_date, \'%Y-%m-%d\'),"") AS inactive_date')
            ->leftJoin('account_types', 'accounts.type_id', '=', 'account_types.id')
            ->leftJoin('account_classifications', 'accounts.classification_id', '=', 'account_classifications.id')
            ->leftJoin('account_types AS c', function ($join) {
                $join->on('accounts.sub_type_id', '=', 'c.id');
            })
            ->where('accounts.deleted_at', '=', null)
            ->where(
                fn($q) => $q->where('accounts.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('accounts.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('account_types.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('accounts.code', 'Like', '%' . request('query') . '%')
                    ->orWhere('accounts.active_date', 'Like', '%' . request('query') . '%')
            )->get();
        LogActivity::addLog();

        return $this->respondAll($accounts);
    }

    public function mergeAccounts()
    {
        $accounts = Account::select(
            'accounts.id',
            'accounts.name as account',
            'accounts.notes as pulpo_code',
            'account_types.name as acc_type',
            'doctors.name as doctor',
            'doctors.id as doctor_id',
            'accounts.code as code',
            'bricks.name as brick',
            'bricks.id as brick_id',
            'specialities.name as speciality',
            'specialities.id as speciality_id',
        )
            ->whereIn(
                'accounts.id',
                [
                    119751,
                    160617,
                    119752,
                    160618
                ]
            )
            ->orderBy('accounts.notes', 'Asc')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->where('account_types.id', 1)
            ->where('accounts.is_merged', 0)
            ->leftJoin(
                'account_lines',
                function ($join) {
                    $join->on('accounts.id', '=', 'account_lines.account_id')
                        ->where('account_lines.line_id', 1);
                }
            )
            ->leftJoin(
                'new_account_doctors',
                function ($join) {
                    $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                    $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                        ->where('new_account_doctors.line_id', 1);
                }
            )
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')

            // ->where('account_lines.line_id', 1)
            // ->where('new_account_doctors.line_id', 1)
            // ->where(fn ($q) => $q->where('accounts.id', 'Like', '%' . request('query') . '%')
            //     ->orWhere('accounts.name', 'Like', '%' . request('query') . '%')
            //     ->orWhere('bricks.name', 'Like', '%' . request('query') . '%')
            //     ->orWhere('bricks.name', 'Like', '%' . request('query') . '%')
            //     ->orWhere('account_types.name', 'Like', '%' . request('query') . '%')
            //     ->orWhere('accounts.code', 'Like', '%' . request('query') . '%')
            //     ->orWhere('accounts.active_date', 'Like', '%' . request('query') . '%'))

            ->get()
            // ->unique(function ($item) {
            //     return $item['id'] . $item['brick_id'] . $item['speciality_id'];
            // })
            ->unique('id')
            ->values();

        return $this->respond($accounts);
    }

    public function saveMergedAccounts(Request $request)
    {
        $mainAccount = $request->accounts[0];
        $accounts = $request->accounts;
        DB::transaction(function () use ($accounts, $mainAccount) {
            for ($i = count($accounts) - 1; $i > 0; $i--) {
                // if ($mainAccount['speciality_id'] == $accounts[$i]['speciality_id']) {
                PlanVisit::where('account_id', $accounts[$i]['id'])->update([
                    'new_account_id' => $accounts[$i]['id'],
                    'new_account_dr_id' => $accounts[$i]['doctor_id'],
                    'account_id' => $mainAccount['id'],
                    'account_dr_id' => $mainAccount['doctor_id'],
                ]);
                ActualVisit::where('account_id', $accounts[$i]['id'])->update([
                    'new_account_id' => $accounts[$i]['id'],
                    'new_account_dr_id' => $accounts[$i]['doctor_id'],
                    'account_id' => $mainAccount['id'],
                    'account_dr_id' => $mainAccount['doctor_id'],
                ]);
                CommercialDoctor::where('doctor_id', $accounts[$i]['doctor_id'])->update([
                    'account_id' => $mainAccount['id'],
                    'doctor_id' => $mainAccount['doctor_id'],
                ]);
                CommercialDoctorCostType::where('doctor_id', $accounts[$i]['doctor_id'])->update([
                    'doctor_id' => $mainAccount['doctor_id'],
                ]);
                AccountLines::where('account_id', $accounts[$i]['id'])->delete();
                NewAccountDoctor::where('account_id', $accounts[$i]['id'])->delete();
                Account::where('id', $accounts[$i]['id'])->delete();
                Doctor::where('id', $accounts[$i]['doctor_id'])->delete();
                Account::find($mainAccount['id'])->update(['is_merged' => 1]);
                // }
            }
        });

        return $this->respondSuccess();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $types = AccountType::orderBy('sort', 'ASC')->select('account_types.id', 'account_types.name')->whereNull('deleted_at')->get();
        $classifications = AccountClassification::orderBy('sort', 'ASC')
            ->select('account_classifications.id', 'account_classifications.name')->get();
        return response()->json([
            'types' => $types,
            'classifications' => $classifications,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(AccountRequest $request)
    {
        $account = Account::create($request->validated());
        $model_id = $account->id;
        $model_type = ('App\Account');
        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success', 'account' => $account]);
    }

    /**
     * Display the specified resource.
     *
     * @param \App\Account $account
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $account = DB::table('accounts')
            ->select(
                'accounts.id',
                'accounts.code',
                'accounts.name',
                'account_types.name as type',
                'account_classifications.name as classification',
                'c.name as sub type',
                'accounts.address',
                'accounts.tel',
                'accounts.mobile',
                'accounts.email',
                'accounts.website_link',
                'accounts.notes',
                'accounts.active_date',
                'accounts.inactive_date'
            )
            ->leftJoin('account_types', 'accounts.type_id', '=', 'account_types.id')
            ->leftJoin('account_classifications', 'accounts.classification_id', '=', 'account_classifications.id')
            ->leftJoin('account_types AS c', function ($join) {
                $join->on('accounts.sub_type_id', '=', 'c.id');
            })
            ->where('accounts.id', '=', $id)
            ->first();

        $account_lines = DB::table('account_lines')
            ->select(
                'account_lines.id',
                'lines.name as line_name',
                'line_divisions.name as line_division_name',
                'bricks.name as brick_name',
                'classes.name as class',
                'account_lines.from_date',
                'account_lines.to_date'
            )
            ->leftJoin('lines', 'account_lines.line_id', '=', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', '=', 'line_divisions.id')
            ->leftJoin('classes', 'account_lines.class_id', '=', 'classes.id')
            ->leftJoin('bricks', 'account_lines.brick_id', '=', 'bricks.id')
            ->where('account_lines.account_id', '=', $id)
            ->where('account_lines.deleted_at', '=', null)
            ->get();

        $account_doctors = DB::table('new_account_doctors')
            ->select(
                'new_account_doctors.id',
                'doctors.name as doctor_name',
                'new_account_doctors.from_date',
                'new_account_doctors.to_date'
            )
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', '=', 'doctors.id')
            ->where('new_account_doctors.account_id', '=', $id)
            ->where('new_account_doctors.deleted_at', '=', null)
            ->get();


        $setting = LinkedParmaciesSetting::where('key', 'type')->value('linked_from');
        $pharmacies = LinkedPharmacy::select('account_id', 'accounts.name as account')
            ->whereNull('linked_pharmacies.deleted_at')
            ->join('accounts', 'linked_pharmacies.account_id', 'accounts.id');
        if ($setting == 'mapping') {
            $pharmacies = $pharmacies->selectRaw('crm_mappings.code as code')
                ->selectRaw('crm_mappings.name as pharmacy')
                ->join('mappings', 'linked_pharmacies.pharmable_id', 'mappings.id')
                ->where('pharmable_type', Mapping::class);
        }
        if ($setting == 'unified') {
            $pharmacies = $pharmacies->selectRaw('crm_mapping_unified_codes.code as code')
                ->selectRaw('crm_mapping_unified_codes.name as pharmacy')
                ->join('mapping_unified_codes', 'linked_pharmacies.pharmable_id', 'mapping_unified_codes.id')
                ->where('pharmable_type', MappingUnifiedCode::class);
        }
        $pharmacies = $pharmacies->where('account_id', $account->id)->get();

        /**@var User */
        $user = Auth::user();
        $roles = collect([]);
        $users = $user->indexPerUser($user);
        if (count($users) == 0) {
            $users = User::get()->pluck('id');
        }
        $getVisis = ActualVisit::whereIntegerInRaw('user_id', $users)->where('account_id', $id);
        $visits = $getVisis->latest()->take(50)->get()->map(function ($actual) {
            return [
                'id' => $actual->id,
                'visit_date' => Carbon::parse($actual->visit_date)->format('Y-M-d'),
                'employee' => $actual->user->fullname,
                'type' => $actual->visitType->name,
                'line' => $actual->line->name,
                'color' => $actual->user->divisions->first()?->DivisionType?->color,
                'division' => $actual->division->name,
                'brick' => $actual->brick->name ?? '',
                'account' => $actual->account?->name,
                'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                'acc_type' => $actual->accountType?->name ?? "",
            ];
        });;
        $userVisits = $getVisis->pluck('user_id')->unique()->values();
        $filtered = User::whereIntegerInRaw('id', $userVisits)->get();

        foreach ($filtered as $user) {
            $roles = $roles->merge($user->roles);
        }
        $roles = $roles->unique('id')->whereNotIn('name', ['admin', 'user', 'guest'])->values();
        $roles = $roles->unique('id')->whereNotIn('name', ['user', 'guest'])->values();

        $socials = DB::table('account_socials')
            ->select('account_socials.id', 'accounts.name as account_name', 'socials.name as social_name', 'account_socials.link')
            ->leftJoin('accounts', 'account_socials.account_id', '=', 'accounts.id')
            ->leftJoin('socials', 'account_socials.social_id', '=', 'socials.id')
            ->where('account_socials.account_id', '=', $id)
            ->where('account_socials.deleted_at', '=', null)
            ->get();
        $commercialDoctors = CommercialRequest::whereHas('doctors', fn($q) => $q->where('account_id', $id))->get()->map(function ($commercialRequest) {
            return [
                'id' => $commercialRequest->id ?? '',
                'type' => $commercialRequest->requestType?->name ?? '',
                'employee' => $commercialRequest->user?->fullname ?? '',
                'from' => Carbon::parse($commercialRequest->from_date)->toDateString() ?? '',
                'to' => Carbon::parse($commercialRequest->to_date)->toDateString() ?? '',
                'amount' => $commercialRequest->amount,

            ];
        });

        $model_id = $id;
        $model_type = Account::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'account' => $account,
            'socials' => $socials,
            'pharmacies' => $pharmacies,
            'account_lines' => $account_lines,
            'account_doctors' => $account_doctors,
            'commercialDoctors' => $commercialDoctors,
            'roles' => $roles,
            'visits' => $visits
        ]);
    }

    public function getVisits($id, Role $role)
    {
        $lineUsers = LineUser::whereNull('deleted_at')->where('line_users.from_date', '<=', now())
            ->where(fn($q) => $q->where('line_users.to_date', '>', (string)Carbon::now())
                ->orWhere('line_users.to_date', null))->get();
        $users = [];
        foreach ($lineUsers as $lineUser) {
            $user = $lineUser->user;
            if ($user->menuroles == $role->name) {
                array_push($users, $user->id);
            }
        }
        $visits = ActualVisit::whereIn('user_id', $users)->where('account_id', $id)->get()->map(function ($actual) {
            return [
                'id' => $actual->id,
                'visit_date' => Carbon::parse($actual->created_at)->format('Y-M-d'),
                'color' => $actual->user->divisions->first()?->DivisionType?->color,
                'employee' => $actual->user->fullname,
                'type' => $actual->visitType->name,
                'line' => $actual->line->name,
                'division' => $actual->division->name,
                'brick' => $actual->brick->name ?? '',
                'account' => $actual->account?->name,
                'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                'acc_type' => $actual->accountType?->name ?? "",
            ];
        });
        return $this->respond($visits);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Account $account
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $account = Account::find($id);
        $types = AccountType::orderBy('sort', 'ASC')->select('account_types.id', 'account_types.name')->get();
        $classifications = AccountClassification::orderBy('sort', 'ASC')
            ->select('account_classifications.id', 'account_classifications.name')->get();
        $subtypes = AccountType::orderBy('sort', 'ASC')->select('account_types.id', 'account_types.name')->where('parent_id', $account->type_id)->get();
        $model_id = $id;
        $model_type = ('App\Account');
        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'account' => $account,
            'types' => $types,
            'classifications' => $classifications,
            'subtypes' => $subtypes
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Account $account
     * @return \Illuminate\Http\Response
     */
    public function update(AccountRequest $request, $id)
    {

        $account = Account::find($id);
        $account->code = $request->input('code');
        $account->name = $request->input('name');
        $account->notes = $request->input('notes');
        $account->type_id = $request->input('type_id');
        $account->classification_id = $request->input('classification_id');
        $account->sub_type_id = $request->input('sub_type_id');
        $account->address = $request->input('address');
        $account->tel = $request->input('tel');
        $account->mobile = $request->input('mobile');
        $account->email = $request->input('email');
        $account->website_link = $request->input('website_link');
        $account->active_date = $request->input('active_date');
        $account->inactive_date = $request->input('inactive_date');
        $account->save();
        $model_id = $id;
        $model_type = ('App\Account');
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success', 'account' => $account]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Account $account
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model_id = $id;
        $model_type = Account::class;
        $account = Account::find($id);
        if ($account) {
            $account->delete();
        }
        // $account_lines = DB::table("account_lines")->where("account_id", $id)->count();
        // $account_doctors = DB::table("account_doctors")->where("account_id", $id)->count();
        // $account_socials = DB::table("account_socials")->where("account_id", $id)->count();
        // if($account){
        //     if ($account_lines > 0 || $account_doctors > 0 || $account_socials > 0)
        //     {
        //         return response()->json(['statusText'=>'failed'],422);
        //     }
        AccountLines::where("account_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        $doctors = NewAccountDoctor::where("account_id", $id)->pluck('id');
        Doctor::whereIn('id', $doctors)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        NewAccountDoctor::where("account_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        AccountSocial::where("account_id", $id)->update([
            'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);

        // $account->delete();
        // }
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function import(ImportRequest $request)
    {
        Account::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        Account::import(
            request: $request,
            update: true
        );

        return $this->respondSuccess();
    }

    public function exportaccounts()
    {
        return Account::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return Account::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $accounts = Account::with(['type', 'sub_type', 'accountlines', 'accountdoctors'])->get();
        return Account::exportPdf($accounts);
    }

    public function sendmail(MailRequest $request)
    {
        $accounts = Account::with(['type', 'sub_type', 'accountlines', 'accountdoctors'])->get();
        return Account::sendMail($request, $accounts);
    }


    public function getAccountLines(Account $account)
    {

        return response()->json(['lines' => $account->lines()->select('lines.id', 'lines.name')->get()]);
    }

    public function getAccountDivisions(Account $account, Line $line)
    {
        return response()->json(['divisions' => $account->divisions()
            ->select('line_divisions.id', 'line_divisions.name')
            ->where("account_lines.line_id", $line->id)->get()]);
    }

    public function getAccountBricks(Account $account, LineDivision $lineDivision)
    {
        return response()->json(['bricks' => $account->bricks()
            ->select('bricks.id', 'bricks.name')
            ->where("account_lines.line_division_id", $lineDivision->id)->get()]);
    }

    public function assignAccountLocation(Request $request, Account $account)
    {
        $account
            ->accountlines()
            ->where([
                "line_id" => $request->line,
                "brick_id" => $request->brick,
                "line_division_id" => $request->division
            ])
            ->update([
                'll' => $request->ll,
                'lg' => $request->lg,
            ]);

        return $this->respondSuccess();
    }

    /**
     * Bulk update account locations
     *
     * @param AccountBulkUpdateLocationsRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkUpdateLocations(AccountBulkUpdateLocationsRequest $request)
    {
        $locationUpdateService = new AccountLocationUpdateService();

        $result = $locationUpdateService->bulkUpdateLocations(
            $request->input('updates'),
            $request->input('analysis_metadata')
        );

        if ($result['success']) {
            return response()->json($result);
        } else {
            return response()->json($result, 500);
        }
    }
}
