<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Controllers\Controller;
use App\Models\QuizCategory;
use App\Http\Requests\QuizRequest;
use App\Models\Quiz;
use App\Models\QuizDetail;
use App\Models\QuizQuestion;
use App\Models\QuizAnswer;
use App\Line;
use App\Models\QuizLine;
use App\Models\QuizResult;
use App\Notifications\QuizCreatedNotification;
use App\Product;
use App\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class QuizController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        /**@var User $user */
        $user = Auth::user();
        $users = $user->indexPerUser($user);
        $quizzes = Quiz::select(
            'quizzes.id as quiz_id',
            'quizzes.name as quiz_name',
            DB::raw('IFNULL(crm_quizzes.start_date,"") as start_date'),
            DB::raw('IFNULL(crm_quizzes.end_date,"") as end_date'),
            DB::raw('IFNULL(crm_quizzes.start_time,"") as start_time'),
            DB::raw('IFNULL(crm_quizzes.end_time,"") as end_time'),
            // 'quizzes.start_date as start_date',
            // 'quizzes.start_time as start_time',
            // 'quizzes.end_date as end_date',
            // 'quizzes.end_time as end_time',
            'quizzes.time as time',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
        )
            // ->leftJoin('quiz_answers', 'quiz_result.answer_id', 'quiz_answers.id')
            // ->leftJoin('quiz_questions', 'quiz_result.question_id', 'quiz_questions.id')
            // ->leftJoin('quizzes', 'quiz_result.quiz_id', 'quizzes.id')
            ->leftJoin('quiz_lines', 'quiz_lines.quiz_id', 'quizzes.id')
            ->leftJoin('lines', 'quiz_lines.line_id', 'lines.id');
        if (count($users) > 0) {
            $quizzes = $quizzes->whereIntegerInRaw('quizzes.user_id', $users->values());
        }
        $quizzes = $quizzes->groupBy('quizzes.id', 'quizzes.name')
            ->get();
        // ->map(function ($quiz) {
        //     return [
        //         'id' => $quiz->id,
        //         'name' => $quiz->name,
        //         'line' => $quiz->line->name,
        //         'date' => $quiz->date,
        //         'time' => $quiz->time,
        //     ];
        // });


        LogActivity::addLog();
        return $this->respond($quizzes);
    }

    public function getLineUsers(Line $line)
    {
        $users = $line->users()->get();
        $products = $line->products()->get();
        $divisions = $line->divisions()->get();
        // throw new CrmException($users);
        return $this->respond(compact('users', 'products', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(QuizRequest $request)
    {
        // throw new CrmException($request->all());

        $request->validated;
        /**@var User */
        $user = Auth::user();
        DB::transaction(function () use ($request) {

            $start_date = Carbon::parse($request->start_date)->setTimeFromTimeString($request->start_time);
            $end_date = Carbon::parse($request->end_date)->setTimeFromTimeString($request->end_time);

            $quiz = new Quiz();
            $quiz->user_id =  Auth::user()->id;
            $quiz->name = $request->name;
            $quiz->desc = $request->desc;

            $quiz->start_date = $start_date;
            $quiz->start_time = $request->start_time;
            $quiz->end_date = $end_date;
            $quiz->end_time = $request->end_time;
            $quiz->quiz_degree = $request->quiz_degree;
            $quiz->success_degree = $request->success_degree;
            $quiz->time = $request->time;
            $quiz->save();
            $last_quiz = Quiz::latest()->first();
            if (!empty($request->line_id)) {
                foreach ($request->line_id as $line) {
                    QuizLine::create([
                        'line_id' => $line,
                        'quiz_id' => $last_quiz->id,
                    ]);
                }
            }
            $last_quiz->products()->attach($request->product_id);
            $last_quiz->users()->attach($request->user_id);
            foreach ($request->quizzes as $key => $detail) {

                QuizDetail::create([
                    'quiz_id' => $last_quiz->id,
                    'quiz_category_id' => $detail['category_id'],
                    'quiz_question_level_id' => $detail['level_id'],
                    'question_number' => $detail['question_number'],
                    'degree' => $detail['degree'],
                ]);
            }
            NotificationHelper::send(
                collect($last_quiz->users),
                new QuizCreatedNotification('Quiz Created', auth()->user())
            );
        });


        return $this->respondCreated();
    }




    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Quiz  $quiz
     * @return \Illuminate\Http\Response
     */
    public function show(Quiz $quiz, $id)
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->lines()->pluck('lines.id');

        $products = Product::whereHas(
            'lineproducts',
            fn($q) =>
            $q->whereIntegerInRaw('line_products.line_id', $lines)->where('line_products.from_date', '<=', now())
                ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                    ->orWhere('line_products.to_date', null))
        )->get()->pluck('id');
        $quiz = Quiz::with('quizDetails')->where("id", $id)->first();
        // $quiz->user_start_date_time = Carbon::now()->toDateTimeString();
        $userStart = Carbon::now()->toDateTimeString();
        $quizEnd = Carbon::parse($quiz->end_date);

        // Get difference in minutes
        $totalAvailableTime = $quizEnd->diffInMinutes($userStart);
        $quiz->totalAvailableTime = $totalAvailableTime;
        // throw new CrmException($products);
        $all_questions = [];
        foreach ($quiz->quizDetails as $index => $detail) {
            $question[$index] = QuizQuestion::with(['quizAnswers', 'quizCategory'])->whereHas('products', function ($product) use ($products) {
                $product->whereIn('products.id', $products);
            })->where('quiz_category_id', $detail->quiz_category_id)->where('quiz_question_level_id', $detail->quiz_question_level_id)
                ->inRandomOrder()->limit($detail->question_number)->get();
            array_push($all_questions, $question[$index]);
        }
        // $finalquestions = Arr::collapse($all_questions);
        $finalquestions = collect($all_questions)->collapse()->unique('id')->values();
        // throw new CrmException($finalquestions);

        $send = [
            'quiz' => $quiz,
            'products' => $quiz->products()->pluck('name')->implode('name'),
            'questions' => $finalquestions,
        ];
        return $this->respond($send);
    }

    public function user_answers(Request $request)
    {
        $quiz_id = $request->quiz_id;
        $start = Carbon::parse($request->startTime);
        $end = Carbon::parse($request->endTime);
        $time_consumed = $end->diff($start)->format('%H:%I:%S');
        $user_id = Auth::user()->id;
        $withoutAnswers = collect($request->answers)->filter(fn($item) => $item['answer_id'] == null)->values();
        if (count($withoutAnswers) > 0) {
            throw new Exception('Sorry, All questions must be answered');
        }
        foreach ($request->answers as $answer) {
            $question_degree = 0;
            if ($answer['correct'] == 1) {
                $question_degree = QuizDetail::where('quiz_id', $quiz_id)
                    ->where('quiz_category_id', $answer['category_id'])
                    ->where('quiz_question_level_id', $answer['level'])
                    ->first()->degree;
            }
            QuizResult::create([
                'quiz_id' => $quiz_id,
                'user_id' => $user_id,
                'answer_id' => $answer['answer_id'],
                'question_id' => $answer['question_id'],
                'time_consumed' => $time_consumed,
                'result' => $question_degree,
            ]);
        }
        return response()->json(['message' => 'success']);
    }

    public function showAnswers(Quiz $quiz, User $user)
    {
        /**@var User */
        $authUser = Auth::user();
        $data = QuizResult::where('quiz_id', $quiz->id)->where('user_id', $user->id)->get()
            ->map(function ($row) {
                return [
                    'emp_id' => $row->user->id,
                    'employee' => $row->user->name,
                    'emp_code' => $row->user->emp_code ?? '',
                    'quiz' => $row->quiz->name,
                    'category' => $row->question->quizCategory->name,
                    'products' => $row->question->products()->pluck('name')->implode(' , '),
                    'question' => $row->question->name,
                    'answer' => $row->answer->name,
                    'ques_score' => $row->quiz->quiz_degree / $row->quiz->quizDetails()->sum('question_number'),
                    'correct_answer' => $row->question->quizAnswers()->where('correct', 1)->first()->name,
                    'result' => $row->result,
                ];
            });
        $total = collect($data)->sum('result');
        $fields = ['emp_id', 'employee', 'emp_code', 'quiz', 'category', 'products', 'question', 'answer', 'ques_score', 'correct_answer', 'result'];
        return $this->respond(['results' => $data, 'fields' => $fields, 'total_results' => $total]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Quiz  $quiz
     * @return \Illuminate\Http\Response
     */
    public function edit(Quiz $quiz)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Quiz  $quiz
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Quiz $quiz)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Quiz  $quiz
     * @return \Illuminate\Http\Response
     */
    public function destroy(Quiz $quiz, User $user)
    {
        QuizResult::where('quiz_id', $quiz->id)->where('user_id', $user->id)->delete();
        // DB::table('quiz_user')->where('quiz_id', $quiz->id)->where('user_id', $user->id)->delete();

        $model_id = $quiz->id;
        $model_type = Quiz::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }

    // Delete Quiz Data
    public function delete(Quiz $quiz)
    {
        QuizResult::where('quiz_id', $quiz->id)->delete();
        QuizDetail::where('quiz_id', $quiz->id)->delete();
        QuizLine::where('quiz_id', $quiz->id)->delete();
        DB::table('quiz_product')->where('quiz_id', $quiz->id)->delete();
        DB::table('quiz_user')->where('quiz_id', $quiz->id)->delete();
        $quiz->delete();
        $model_id = $quiz->id;
        $model_type = Quiz::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
}
